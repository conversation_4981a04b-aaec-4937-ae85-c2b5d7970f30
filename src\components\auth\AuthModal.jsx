import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Login from './Login';
import SignUp from './SignUp';

const AuthModal = ({ isOpen, onClose, initialMode = 'login' }) => {
  const [mode, setMode] = useState(initialMode);

  const handleSwitchToSignup = () => {
    setMode('signup');
  };

  const handleSwitchToLogin = () => {
    setMode('login');
  };

  const handleClose = () => {
    setMode('login'); // Reset to login mode when closing
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={handleClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2 }}
          className="bg-pastel-light/95 backdrop-blur-md rounded-2xl shadow-2xl w-full max-w-md mx-auto relative border border-pastel-medium/20"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Close Button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="absolute top-4 right-4 text-pastel-dark/50 hover:text-pastel-accent hover:bg-pastel-medium/30 z-10"
          >
            <X size={20} />
          </Button>

          {/* Modal Content */}
          <div className="p-8">
            <AnimatePresence mode="wait">
              {mode === 'login' ? (
                <Login
                  key="login"
                  onSwitchToSignup={handleSwitchToSignup}
                  onClose={handleClose}
                />
              ) : (
                <SignUp
                  key="signup"
                  onSwitchToLogin={handleSwitchToLogin}
                  onClose={handleClose}
                />
              )}
            </AnimatePresence>
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-pastel-accent via-pastel-medium to-pastel-accent rounded-t-2xl"></div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default AuthModal;
